package com.ybmmarket20.view;

import android.content.Context;
import androidx.viewpager.widget.ViewPager;
import android.text.TextUtils;
import android.util.AttributeSet;

import com.flyco.tablayout.SegmentTabLayout;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.ybmmarket20.R;
import com.ybmmarket20.adapter.ViewPagerBaseAdapter;
import com.ybmmarket20.bean.TabProductBean;
import com.ybmmarket20.constant.AppNetConfig;
import com.ybmmarket20.utils.UiUtils;

import java.util.ArrayList;
import java.util.List;

/*
 * tab切换+横向滑动商品列表
 * */
public class DynamicTabProductLayout extends BaseDynamicLayout<TabProductBean> {

    SegmentTabLayout tabs;
    NoScrollView2Pager viewpager;

    private int index = 0;
    private int selPostion;
    private TabProductBean typeViewItem;
    private String imgHeader = AppNetConfig.CDN_HOST;

    private List<TabProductLayout> listView = new ArrayList<>();
    private List<String> titles = new ArrayList<>();
    private List<TabProductBean> list = new ArrayList<>();

    public DynamicTabProductLayout(Context context) {
        this(context, null);
    }

    public DynamicTabProductLayout(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public DynamicTabProductLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

    }

    @Override
    public void initViews() {
        tabs = (SegmentTabLayout) findViewById(R.id.tabs);
        viewpager = (NoScrollView2Pager) findViewById(R.id.viewpager);
    }

    @Override
    public boolean supportSetHei() {
        return false;
    }

    @Override
    public int getLayoutId() {
        return R.layout.dynamic_layout_tab_product;
    }

    @Override
    public void setItemData(List<TabProductBean> items) {
        if (items == null || items.isEmpty()) {
            return;
        }

        if (tabs == null) {
            return;
        }
        if (titles == null) {
            titles = new ArrayList<>();
        }
        titles.clear();

        if (listView == null) {
            listView = new ArrayList<>();
        }
        listView.clear();

        if (list == null) {
            list = new ArrayList<>();
        }
        list.clear();
        list.addAll(items);
        for (int i = 0; i < list.size(); i++) {

            titles.add(list.get(i).text);
            listView.add(new TabProductLayout(getContext(), list.get(i).api, list.get(i).action, moduleView.bgRes, list.get(i).text, moduleView.padding, moduleView.margin));
        }

        viewpager.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (viewpager != null) {

                    if (list != null && list.size() > 0) {
                        tabs.setTextSelectColor(setNetColor(list.get(0).titleColorchecked));
                        tabs.setTextUnselectColor(setNetColor(list.get(0).textColor));
                        tabs.setBarColor(setNetColor(list.get(0).bgColor));
                        tabs.setIndicatorColor(setNetColor(list.get(0).bgColorchecked));
                    }
                }
            }
        }, 300);

        ViewPagerBaseAdapter viewPagerAdapter = new ViewPagerBaseAdapter(listView) {
            @Override
            public CharSequence getPageTitle(int position) {
                return titles.get(position);
            }
        };
        viewpager.setAdapter(viewPagerAdapter);
        String[] array = titles.toArray(new String[titles.size()]);
        tabs.setTabData(array);
        tabs.setCurrentTab(0);
        tabs.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
                viewpager.setCurrentItem(position);
            }

            @Override
            public void onTabReselect(int position) {
            }
        });
        viewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {

            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

//        if (tab > 0 && tab < titles.size()) {
//            viewpager.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    if (viewpager != null) {
//                        viewpager.setCurrentItem(tab, false);
//                        tabs.setCurrentTab(tab);
//                        //((TabProductLayout) listView.get(tab)).selPostion(index);
//                    }
//                }
//            }, 100);
//        }

    }

    public int setNetColor(String color) {

        if (TextUtils.isEmpty(color)) {
            return 0;
        }
        if (color.startsWith("#")) {//设置背景色
            return getColor(color);
        }
        return 0;
    }

    @Override
    public void setStyle(int style) {

    }
}
