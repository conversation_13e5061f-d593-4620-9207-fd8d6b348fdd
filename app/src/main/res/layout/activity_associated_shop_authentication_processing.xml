<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/nv"
        layout="@layout/common_header_items" />

    <TextView
        android:id="@+id/hintTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFF7EF"
        android:paddingStart="20dp"
        android:paddingTop="10dp"
        android:paddingEnd="20dp"
        android:paddingBottom="10dp"
        android:text="*关联店铺资质审核中，预计24小时内完成审核，请您耐心等待，审核通过后可登入药帮忙App"
        android:textColor="#99664d"
        android:textSize="@dimen/dimen_dp_12"
        app:layout_constraintTop_toBottomOf="@+id/nv"
        tools:layout_editor_absoluteX="0dp" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/ilShopName"
        android:layout_width="0dp"
        android:layout_height="43dp"
        android:layout_marginTop="9dp"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:textColorHint="@color/loginTextAppearance"
        app:hintTextAppearance="@style/inputLayoutHintAppearance"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/hintTv">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etShopName"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="店铺名称"
            android:focusableInTouchMode="false"
            android:singleLine="true"
            android:textColor="#292933"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_f5f5f5"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_4"
        app:layout_constraintTop_toBottomOf="@+id/ilShopName" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/ilAddress"
        android:layout_width="0dp"
        android:layout_height="43dp"
        android:layout_marginTop="@dimen/dimen_dp_4"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:textColorHint="@color/loginTextAppearance"
        app:hintTextAppearance="@style/inputLayoutHintAppearance"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ilShopName">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etAddress"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="店铺所在省/市/区"
            android:focusableInTouchMode="false"
            android:singleLine="true"
            android:textColor="#292933"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_f5f5f5"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_4"
        app:layout_constraintTop_toBottomOf="@+id/ilAddress" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/ilAddressDetail"
        android:layout_width="0dp"
        android:layout_height="43dp"
        android:layout_marginTop="9dp"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:textColorHint="@color/loginTextAppearance"
        app:hintTextAppearance="@style/inputLayoutHintAppearance"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ilAddress">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etAddressDetail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="店铺门牌/详细地址"
            android:focusableInTouchMode="false"
            android:singleLine="true"
            android:textColor="#292933"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <View
        android:id="@+id/viewCompanyType"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_f5f5f5"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_4"
        app:layout_constraintTop_toBottomOf="@+id/ilAddressDetail" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/ilCompanyType"
        android:layout_width="0dp"
        android:layout_height="43dp"
        android:layout_marginTop="9dp"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:textColorHint="@color/loginTextAppearance"
        app:hintTextAppearance="@style/inputLayoutHintAppearance"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ilAddressDetail">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etCompanyType"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="企业类型"
            android:focusableInTouchMode="false"
            android:singleLine="true"
            android:textColor="#292933"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupCompanyType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="viewCompanyType, ilCompanyType" />

    <View
        android:id="@+id/viewNumber"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_f5f5f5"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:layout_marginTop="@dimen/dimen_dp_4"
        app:layout_constraintTop_toBottomOf="@+id/ilCompanyType" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/ilNumber"
        android:layout_width="0dp"
        android:layout_height="43dp"
        android:layout_marginTop="9dp"
        android:layout_marginStart="@dimen/dimen_dp_20"
        android:layout_marginEnd="@dimen/dimen_dp_20"
        android:textColorHint="@color/loginTextAppearance"
        app:hintTextAppearance="@style/inputLayoutHintAppearance"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ilCompanyType">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etNumber"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="营业执照"
            android:focusableInTouchMode="false"
            android:singleLine="true"
            android:textColor="#292933"
            android:textSize="16sp" />
    </com.google.android.material.textfield.TextInputLayout>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupNumber"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="viewNumber, ilNumber" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent">

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_1"
        android:background="@color/colors_DDDDDD"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        app:layout_constraintBottom_toTopOf="@+id/spaceBottom" />

    <Space
        android:id="@+id/spaceBottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_44"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tvQuite"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:text="退出"
        android:gravity="center"
        android:textColor="@color/color_00b377"
        android:textSize="@dimen/dimen_dp_18"
        android:background="@drawable/shape_button_quite"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginEnd="@dimen/dimen_dp_5"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvOtherShop"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvOtherShop"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:text="登录其他店铺"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="@dimen/dimen_dp_18"
        android:background="@drawable/shape_button_other_shop"
        android:layout_marginStart="@dimen/dimen_dp_5"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvQuite"
        app:layout_constraintTop_toTopOf="@+id/tvQuite" />

    <TextView
        android:id="@+id/tvQuiteSingle"
        android:layout_width="@dimen/dimen_dp_0"
        android:layout_height="@dimen/dimen_dp_44"
        android:text="退出"
        android:gravity="center"
        android:textColor="@color/color_00b377"
        android:textSize="@dimen/dimen_dp_18"
        android:background="@drawable/shape_button_quite"
        android:layout_marginStart="@dimen/dimen_dp_15"
        android:layout_marginEnd="@dimen/dimen_dp_15"
        android:layout_marginBottom="@dimen/dimen_dp_8"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>