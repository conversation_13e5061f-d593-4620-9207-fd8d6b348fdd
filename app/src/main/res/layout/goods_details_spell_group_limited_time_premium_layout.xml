<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ll_spell_group_limited_time_premium"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:visibility="gone"
    android:orientation="horizontal"
    tools:visibility="visible">

    <!--拼团价-->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_spell_group_01"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_dp_60"
        android:background="@color/color_ff2121" >

        <!-- 创建一个容器来包含拼团价标题和价格，用于垂直居中对齐 -->
        <LinearLayout
            android:id="@+id/ll_limited_time_price_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="10dp">

            <!-- 第一行：拼团价标题和价格 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:baselineAligned="true"
                android:gravity="bottom">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="拼团价"
                    android:textColor="@color/white"
                    android:textSize="14dp"
                    android:gravity="bottom"
                    android:layout_gravity="bottom" />

                <TextView
                    android:id="@+id/tv_spell_limit_time_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_3"
                    android:textColor="@color/white"
                    android:textSize="14dp"
                    android:textStyle="bold"
                    android:gravity="bottom"
                    android:layout_gravity="bottom"
                    tools:text="¥15.25" />

                <TextView
                    android:id="@+id/tv_spell_limit_time_origin_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dimen_dp_5"
                    android:background="@drawable/shape_spell_group_origin_price"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:visibility="gone"
                    android:gravity="bottom"
                    android:layout_gravity="bottom"
                    tools:text="¥10.25"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- 第二行：单价和限时内容 -->
            <LinearLayout
                android:id="@+id/ll_limited_time_second_line"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp"
                android:visibility="gone">

                <!-- 单价显示 -->
                <TextView
                    android:id="@+id/tv_unit_price_limited_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="11dp"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    tools:text="单价"
                    tools:visibility="visible" />

                <!-- 分隔线 -->
                <View
                    android:id="@+id/view_limited_time_divider"
                    android:layout_width="1dp"
                    android:layout_height="12dp"
                    android:background="@color/white"
                    android:visibility="gone"
                    android:layout_marginStart="6dp" />

                <TextView
                    android:id="@+id/tv_spell_group_limit_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:textSize="12dp"
                    android:textColor="@color/color_CCFFFFFF"
                    android:visibility="gone"
                    tools:text="限时前2件29.5元" />

            </LinearLayout>

        </LinearLayout>

        <ImageView
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="9dp"
            android:layout_marginEnd="22dp"
            android:src="@drawable/icon_goods_details_limited_time_premium"
            android:layout_width="84dp"
            android:layout_height="16dp"/>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_time_hundred_ms"
            android:layout_width="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            app:rv_backgroundColor="@color/white"
            android:gravity="center"
            app:rv_cornerRadius="2dp"
            android:paddingHorizontal="1dp"
            android:textSize="13dp"
            tools:text="9"
            android:textColor="@color/color_ff2121"
            android:minWidth="19dp"
            android:layout_height="19dp"/>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_time_s"
            android:layout_width="wrap_content"
            app:layout_constraintEnd_toStartOf="@id/tv_time_hundred_ms"
            app:layout_constraintTop_toTopOf="@id/tv_time_hundred_ms"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_hundred_ms"
            android:layout_marginEnd="9dp"
            app:rv_backgroundColor="@color/white"
            android:gravity="center"
            app:rv_cornerRadius="2dp"
            android:paddingHorizontal="1dp"
            android:textSize="13dp"
            tools:text="49"
            android:textColor="@color/color_ff2121"
            android:minWidth="19dp"
            android:layout_height="19dp"/>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_time_min"
            android:layout_width="wrap_content"
            app:layout_constraintEnd_toStartOf="@id/tv_time_s"
            app:layout_constraintTop_toTopOf="@id/tv_time_s"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_s"
            android:layout_marginEnd="9dp"
            app:rv_backgroundColor="@color/white"
            android:gravity="center"
            app:rv_cornerRadius="2dp"
            android:paddingHorizontal="1dp"
            android:textSize="13dp"
            tools:text="999"
            android:textColor="@color/color_ff2121"
            android:minWidth="19dp"
            android:layout_height="19dp"/>

        <com.ybmmarket20.common.widget.RoundTextView
            android:id="@+id/tv_time_h"
            android:layout_width="wrap_content"
            app:layout_constraintEnd_toStartOf="@id/tv_time_min"
            app:layout_constraintTop_toTopOf="@id/tv_time_min"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_min"
            android:layout_marginEnd="9dp"
            app:rv_backgroundColor="@color/white"
            android:gravity="center"
            app:rv_cornerRadius="2dp"
            android:paddingHorizontal="1dp"
            android:textSize="13dp"
            tools:text="9999"
            android:textColor="@color/color_ff2121"
            android:minWidth="19dp"
            android:layout_height="19dp"/>

        <TextView
            android:id="@+id/tv_end"
            android:text="距结束"
            android:textColor="@color/white"
            android:textSize="12dp"
            android:layout_marginEnd="5dp"
            app:layout_constraintEnd_toStartOf="@id/tv_time_h"
            app:layout_constraintTop_toTopOf="@id/tv_time_h"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_h"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_mark_ms_s"
            android:layout_width="wrap_content"
            app:layout_constraintStart_toEndOf="@id/tv_time_s"
            app:layout_constraintEnd_toStartOf="@id/tv_time_hundred_ms"
            android:textColor="@color/white"
            android:textSize="13dp"
            android:text="."
            app:layout_constraintBaseline_toBaselineOf="@id/tv_time_hundred_ms"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_mark_min_s"
            android:layout_width="wrap_content"
            app:layout_constraintStart_toEndOf="@id/tv_time_min"
            app:layout_constraintEnd_toStartOf="@id/tv_time_s"
            android:textColor="@color/white"
            android:textSize="13dp"
            android:text=":"
            app:layout_constraintTop_toTopOf="@id/tv_time_s"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_s"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_mark_h_min"
            android:layout_width="wrap_content"
            app:layout_constraintStart_toEndOf="@id/tv_time_h"
            app:layout_constraintEnd_toStartOf="@id/tv_time_min"
            android:textColor="@color/white"
            android:textSize="13dp"
            android:text=":"
            app:layout_constraintTop_toTopOf="@id/tv_time_min"
            app:layout_constraintBottom_toBottomOf="@id/tv_time_min"
            android:layout_height="wrap_content"/>


        <TextView
            android:id="@+id/tv_spell_group_aptitude"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_18"
            android:text="价格认证资质可见"
            android:visibility="gone"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_15" />

        <TextView
            android:id="@+id/tv_spell_group_control"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginStart="@dimen/dimen_dp_18"
            android:text="价格签署协议可见"
            android:visibility="gone"
            tools:visibility="gone"
            android:textColor="@color/white"
            android:textSize="@dimen/dimen_dp_15" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_spell_group_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:constraint_referenced_ids="tv_title, tv_spell_limit_time_price" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>